<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-header">
			<view class="search-bar">
				<view class="search-input-wrapper">
					<input
						class="search-input"
						type="text"
						v-model="searchKeyword"
						placeholder="搜索你喜欢的甜品..."
						@input="onSearchInput"
						@confirm="onSearchConfirm"
						focus
					/>
					<view class="search-btn" @click="onSearchConfirm">
						<text class="search-icon">🔍</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 搜索建议 -->
		<view class="search-suggestions" v-if="showSuggestions && suggestions.length > 0">
			<view class="suggestion-title">搜索建议</view>
			<view
				v-for="(suggestion, index) in suggestions"
				:key="index"
				class="suggestion-item"
				@click="selectSuggestion(suggestion)"
			>
				<text class="suggestion-icon">🔍</text>
				<text class="suggestion-text">{{ suggestion }}</text>
			</view>
		</view>

		<!-- 热门搜索 -->
		<view class="hot-search" v-if="!searchKeyword && !hasSearched">
			<view class="section-title">热门搜索</view>
			<view class="hot-tags">
				<view
					v-for="(tag, index) in hotSearchTags"
					:key="index"
					class="hot-tag"
					@click="selectHotTag(tag)"
				>
					{{ tag }}
				</view>
			</view>
		</view>

		<!-- 搜索结果 - 添加下拉刷新和上拉加载 -->
		<scroll-view
			class="search-results-scroll"
			v-if="hasSearched"
			scroll-y="true"
			refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			@refresherrestore="onRefreshRestore"
			@scrolltolower="onLoadMore"
			:lower-threshold="100"
		>
			<view class="search-results">
				<view class="result-header">
					<text class="result-count">找到 {{ totalResults }} 个相关商品</text>
					<view class="filter-btn">
						<text class="filter-text">筛选</text>
						<text class="filter-icon">⚙️</text>
					</view>
				</view>

				<view class="results-list" v-if="displayResults.length > 0">
					<view
						v-for="(product, index) in displayResults"
						:key="index"
						class="result-item"
						@click="goToProduct(product)"
					>
						<view class="product-image" :style="{backgroundColor: product.bgColor}">
							<text class="product-emoji">{{ product.emoji }}</text>
						</view>
						<view class="product-info">
							<text class="product-name">{{ product.name }}</text>
							<text class="product-desc">{{ product.description }}</text>
							<view class="product-price-row">
								<text class="product-price">¥{{ product.price }}</text>
								<view class="add-btn" @click.stop="addToCart(product)">
									<text class="add-icon">+</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 加载更多状态 -->
				<view class="load-more" v-if="displayResults.length > 0 && hasMore">
					<view class="load-more-content" v-if="isLoadingMore">
						<view class="loading-spinner"></view>
						<text class="load-more-text">加载中...</text>
					</view>
					<text class="load-more-text" v-else>上拉加载更多</text>
				</view>

				<!-- 没有更多数据 -->
				<view class="no-more" v-if="displayResults.length > 0 && !hasMore">
					<text class="no-more-text">没有更多商品了</text>
				</view>

				<!-- 无搜索结果 -->
				<view class="no-results" v-if="displayResults.length === 0 && !isLoading">
					<text class="no-results-emoji">😔</text>
					<text class="no-results-text">没有找到相关商品</text>
					<text class="no-results-tip">试试其他关键词吧</text>
				</view>

				<!-- 初始加载状态 -->
				<view class="loading-initial" v-if="isLoading && displayResults.length === 0">
					<view class="loading-spinner"></view>
					<text class="loading-text">搜索中...</text>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted, watch } from 'vue'

	// 响应式数据
	const searchKeyword = ref('')
	const hasSearched = ref(false)
	const showSuggestions = ref(false)

	// 分页和加载状态
	const currentPage = ref(1)
	const pageSize = ref(10) // 每页显示10个商品
	const isLoading = ref(false)
	const isRefreshing = ref(false)
	const isLoadingMore = ref(false)
	const hasMore = ref(true)
	const displayResults = ref([]) // 当前显示的搜索结果

	// 热门搜索标签
	const hotSearchTags = ref([
		'草莓蛋糕', '巧克力', '马卡龙', '泡芙', '布丁', '冰淇淋', '提拉米苏', '芝士蛋糕'
	])

	// 搜索建议
	const suggestions = ref([])

	// 所有商品数据（扩展版本，包含更多商品以便测试上拉下拉功能）
	const allProducts = ref([
		// 蛋糕系列 (30个)
		{ name: '草莓蛋糕', description: '新鲜草莓制作', price: 68, emoji: '🍓', bgColor: '#FFE5E5', category: '蛋糕' },
		{ name: '巧克力蛋糕', description: '浓郁巧克力', price: 78, emoji: '🍫', bgColor: '#F5E6D3', category: '蛋糕' },
		{ name: '芝士蛋糕', description: '免烤芝士', price: 58, emoji: '🧀', bgColor: '#FFF8E1', category: '蛋糕' },
		{ name: '红丝绒蛋糕', description: '经典美式', price: 88, emoji: '❤️', bgColor: '#FFE5E5', category: '蛋糕' },
		{ name: '提拉米苏', description: '意式经典', price: 45, emoji: '☕', bgColor: '#F5E6D3', category: '蛋糕' },
		{ name: '舒芙蕾', description: '轻盈口感', price: 35, emoji: '☁️', bgColor: '#FFF8E1', category: '蛋糕' },
		{ name: '黑森林蛋糕', description: '德式经典', price: 85, emoji: '🌲', bgColor: '#F5E6D3', category: '蛋糕' },
		{ name: '慕斯蛋糕', description: '丝滑慕斯', price: 65, emoji: '🍰', bgColor: '#E8F5E8', category: '蛋糕' },
		{ name: '千层蛋糕', description: '层次丰富', price: 72, emoji: '🎂', bgColor: '#FFF8E1', category: '蛋糕' },
		{ name: '戚风蛋糕', description: '松软香甜', price: 38, emoji: '🧁', bgColor: '#FFE5E5', category: '蛋糕' },
		{ name: '海绵蛋糕', description: '经典基础', price: 32, emoji: '🍰', bgColor: '#FFF8E1', category: '蛋糕' },
		{ name: '榴莲蛋糕', description: '热带风味', price: 95, emoji: '🌟', bgColor: '#FFF3E0', category: '蛋糕' },
		{ name: '抹茶蛋糕', description: '清香淡雅', price: 55, emoji: '🍵', bgColor: '#E8F5E8', category: '蛋糕' },
		{ name: '柠檬蛋糕', description: '清新酸甜', price: 48, emoji: '🍋', bgColor: '#FFFACD', category: '蛋糕' },
		{ name: '香草蛋糕', description: '经典香草', price: 42, emoji: '🤍', bgColor: '#FFF8E1', category: '蛋糕' },
		{ name: '芒果蛋糕', description: '热带芒果', price: 68, emoji: '🥭', bgColor: '#FFF3E0', category: '蛋糕' },
		{ name: '蓝莓蛋糕', description: '酸甜蓝莓', price: 58, emoji: '🫐', bgColor: '#E6F3FF', category: '蛋糕' },
		{ name: '椰子蛋糕', description: '热带椰香', price: 52, emoji: '🥥', bgColor: '#F8F8FF', category: '蛋糕' },
		{ name: '咖啡蛋糕', description: '醇香咖啡', price: 48, emoji: '☕', bgColor: '#F5E6D3', category: '蛋糕' },
		{ name: '焦糖蛋糕', description: '香甜焦糖', price: 55, emoji: '🍮', bgColor: '#F5E6D3', category: '蛋糕' },
		{ name: '樱花蛋糕', description: '浪漫樱花', price: 78, emoji: '🌸', bgColor: '#FFE5E5', category: '蛋糕' },
		{ name: '彩虹蛋糕', description: '七彩缤纷', price: 88, emoji: '🌈', bgColor: '#F0F8FF', category: '蛋糕' },
		{ name: '巧克力熔岩蛋糕', description: '流心巧克力', price: 68, emoji: '🌋', bgColor: '#F5E6D3', category: '蛋糕' },
		{ name: '奶昔蛋糕', description: '奶昔风味', price: 58, emoji: '🥤', bgColor: '#F8F8FF', category: '蛋糕' },
		{ name: '水果蛋糕', description: '新鲜水果', price: 75, emoji: '🍎', bgColor: '#FFE5E5', category: '蛋糕' },
		{ name: '坚果蛋糕', description: '香脆坚果', price: 65, emoji: '🥜', bgColor: '#F5E6D3', category: '蛋糕' },
		{ name: '蜂蜜蛋糕', description: '天然蜂蜜', price: 45, emoji: '🍯', bgColor: '#FFF8E1', category: '蛋糕' },
		{ name: '玫瑰蛋糕', description: '浪漫玫瑰', price: 85, emoji: '🌹', bgColor: '#FFE5E5', category: '蛋糕' },
		{ name: '薄荷蛋糕', description: '清凉薄荷', price: 48, emoji: '🌿', bgColor: '#E8F5E8', category: '蛋糕' },
		{ name: '奥利奥蛋糕', description: '经典奥利奥', price: 58, emoji: '🍪', bgColor: '#F5E6D3', category: '蛋糕' },

		// 马卡龙系列 (25个)
		{ name: '巧克力马卡龙', description: '法式经典', price: 25, emoji: '🍫', bgColor: '#F5E6D3', category: '马卡龙' },
		{ name: '草莓马卡龙', description: '粉嫩可爱', price: 25, emoji: '🍓', bgColor: '#FFE5E5', category: '马卡龙' },
		{ name: '抹茶马卡龙', description: '日式风味', price: 28, emoji: '🍵', bgColor: '#E8F5E8', category: '马卡龙' },
		{ name: '柠檬马卡龙', description: '清香怡人', price: 25, emoji: '🍋', bgColor: '#FFFACD', category: '马卡龙' },
		{ name: '香草马卡龙', description: '经典口味', price: 23, emoji: '🤍', bgColor: '#FFF8E1', category: '马卡龙' },
		{ name: '玫瑰马卡龙', description: '浪漫花香', price: 30, emoji: '🌹', bgColor: '#FFE5E5', category: '马卡龙' },
		{ name: '薰衣草马卡龙', description: '紫色梦幻', price: 32, emoji: '💜', bgColor: '#E6E6FA', category: '马卡龙' },
		{ name: '咖啡马卡龙', description: '醇香浓郁', price: 28, emoji: '☕', bgColor: '#F5E6D3', category: '马卡龙' },
		{ name: '蓝莓马卡龙', description: '酸甜可口', price: 26, emoji: '🫐', bgColor: '#E6F3FF', category: '马卡龙' },
		{ name: '开心果马卡龙', description: '坚果香味', price: 35, emoji: '🥜', bgColor: '#E8F5E8', category: '马卡龙' },
		{ name: '芒果马卡龙', description: '热带芒果', price: 28, emoji: '🥭', bgColor: '#FFF3E0', category: '马卡龙' },
		{ name: '椰子马卡龙', description: '热带椰香', price: 30, emoji: '🥥', bgColor: '#F8F8FF', category: '马卡龙' },
		{ name: '焦糖马卡龙', description: '香甜焦糖', price: 28, emoji: '🍮', bgColor: '#F5E6D3', category: '马卡龙' },
		{ name: '樱花马卡龙', description: '浪漫樱花', price: 35, emoji: '🌸', bgColor: '#FFE5E5', category: '马卡龙' },
		{ name: '黑加仑马卡龙', description: '酸甜黑加仑', price: 32, emoji: '🍇', bgColor: '#E6E6FA', category: '马卡龙' },
		{ name: '蜂蜜马卡龙', description: '天然蜂蜜', price: 26, emoji: '🍯', bgColor: '#FFF8E1', category: '马卡龙' },
		{ name: '奶茶马卡龙', description: '港式奶茶', price: 30, emoji: '🧋', bgColor: '#F5E6D3', category: '马卡龙' },
		{ name: '芝士马卡龙', description: '浓郁芝士', price: 32, emoji: '🧀', bgColor: '#FFF8E1', category: '马卡龙' },
		{ name: '红豆马卡龙', description: '日式红豆', price: 28, emoji: '🔴', bgColor: '#FFE5E5', category: '马卡龙' },
		{ name: '绿茶马卡龙', description: '清香绿茶', price: 28, emoji: '🍃', bgColor: '#E8F5E8', category: '马卡龙' },
		{ name: '榛子马卡龙', description: '香脆榛子', price: 35, emoji: '🌰', bgColor: '#F5E6D3', category: '马卡龙' },
		{ name: '杏仁马卡龙', description: '经典杏仁', price: 30, emoji: '🥜', bgColor: '#F5E6D3', category: '马卡龙' },
		{ name: '薄荷马卡龙', description: '清凉薄荷', price: 26, emoji: '🌿', bgColor: '#E8F5E8', category: '马卡龙' },
		{ name: '橙子马卡龙', description: '清香橙子', price: 25, emoji: '🍊', bgColor: '#FFF3E0', category: '马卡龙' },
		{ name: '紫薯马卡龙', description: '健康紫薯', price: 28, emoji: '🍠', bgColor: '#E6E6FA', category: '马卡龙' },

		// 泡芙系列 (20个)
		{ name: '香草泡芙', description: '奶香浓郁', price: 18, emoji: '🥐', bgColor: '#FFF8E1', category: '泡芙' },
		{ name: '巧克力泡芙', description: '浓郁可可', price: 20, emoji: '🍫', bgColor: '#F5E6D3', category: '泡芙' },
		{ name: '卡仕达泡芙', description: '经典口味', price: 18, emoji: '🥐', bgColor: '#FFF8E1', category: '泡芙' },
		{ name: '抹茶泡芙', description: '清香淡雅', price: 22, emoji: '🍵', bgColor: '#E8F5E8', category: '泡芙' },
		{ name: '草莓泡芙', description: '新鲜果味', price: 20, emoji: '🍓', bgColor: '#FFE5E5', category: '泡芙' },
		{ name: '芒果泡芙', description: '热带风情', price: 22, emoji: '🥭', bgColor: '#FFF3E0', category: '泡芙' },
		{ name: '榴莲泡芙', description: '浓郁香甜', price: 28, emoji: '🌟', bgColor: '#FFF3E0', category: '泡芙' },
		{ name: '奶茶泡芙', description: '港式风味', price: 24, emoji: '🧋', bgColor: '#F5E6D3', category: '泡芙' },
		{ name: '芝士泡芙', description: '浓郁芝士', price: 25, emoji: '🧀', bgColor: '#FFF8E1', category: '泡芙' },
		{ name: '焦糖泡芙', description: '香甜焦糖', price: 21, emoji: '🍮', bgColor: '#F5E6D3', category: '泡芙' },
		{ name: '柠檬泡芙', description: '清香柠檬', price: 20, emoji: '🍋', bgColor: '#FFFACD', category: '泡芙' },
		{ name: '蓝莓泡芙', description: '酸甜蓝莓', price: 22, emoji: '🫐', bgColor: '#E6F3FF', category: '泡芙' },
		{ name: '椰子泡芙', description: '热带椰香', price: 24, emoji: '🥥', bgColor: '#F8F8FF', category: '泡芙' },
		{ name: '咖啡泡芙', description: '醇香咖啡', price: 23, emoji: '☕', bgColor: '#F5E6D3', category: '泡芙' },
		{ name: '樱花泡芙', description: '浪漫樱花', price: 26, emoji: '🌸', bgColor: '#FFE5E5', category: '泡芙' },
		{ name: '红豆泡芙', description: '日式红豆', price: 22, emoji: '🔴', bgColor: '#FFE5E5', category: '泡芙' },
		{ name: '绿茶泡芙', description: '清香绿茶', price: 22, emoji: '🍃', bgColor: '#E8F5E8', category: '泡芙' },
		{ name: '蜂蜜泡芙', description: '天然蜂蜜', price: 20, emoji: '🍯', bgColor: '#FFF8E1', category: '泡芙' },
		{ name: '玫瑰泡芙', description: '浪漫玫瑰', price: 26, emoji: '🌹', bgColor: '#FFE5E5', category: '泡芙' },
		{ name: '薄荷泡芙', description: '清凉薄荷', price: 21, emoji: '🌿', bgColor: '#E8F5E8', category: '泡芙' },

		// 布丁系列 (18个)
		{ name: '芒果布丁', description: '热带风味', price: 22, emoji: '🥭', bgColor: '#FFF3E0', category: '布丁' },
		{ name: '焦糖布丁', description: '香甜焦糖', price: 20, emoji: '🍮', bgColor: '#F5E6D3', category: '布丁' },
		{ name: '抹茶布丁', description: '清香回甘', price: 24, emoji: '🍵', bgColor: '#E8F5E8', category: '布丁' },
		{ name: '牛奶布丁', description: '丝滑香甜', price: 18, emoji: '🥛', bgColor: '#F8F8FF', category: '布丁' },
		{ name: '巧克力布丁', description: '浓郁可可', price: 22, emoji: '🍫', bgColor: '#F5E6D3', category: '布丁' },
		{ name: '草莓布丁', description: '粉嫩可爱', price: 20, emoji: '🍓', bgColor: '#FFE5E5', category: '布丁' },
		{ name: '香草布丁', description: '经典口味', price: 18, emoji: '🤍', bgColor: '#FFF8E1', category: '布丁' },
		{ name: '椰子布丁', description: '热带椰香', price: 25, emoji: '🥥', bgColor: '#F8F8FF', category: '布丁' },
		{ name: '红豆布丁', description: '日式风味', price: 23, emoji: '🔴', bgColor: '#FFE5E5', category: '布丁' },
		{ name: '绿茶布丁', description: '清香淡雅', price: 24, emoji: '🍵', bgColor: '#E8F5E8', category: '布丁' },
		{ name: '柠檬布丁', description: '清香酸甜', price: 20, emoji: '🍋', bgColor: '#FFFACD', category: '布丁' },
		{ name: '蓝莓布丁', description: '酸甜蓝莓', price: 22, emoji: '🫐', bgColor: '#E6F3FF', category: '布丁' },
		{ name: '咖啡布丁', description: '醇香咖啡', price: 23, emoji: '☕', bgColor: '#F5E6D3', category: '布丁' },
		{ name: '樱花布丁', description: '浪漫樱花', price: 26, emoji: '🌸', bgColor: '#FFE5E5', category: '布丁' },
		{ name: '蜂蜜布丁', description: '天然蜂蜜', price: 20, emoji: '🍯', bgColor: '#FFF8E1', category: '布丁' },
		{ name: '玫瑰布丁', description: '浪漫玫瑰', price: 25, emoji: '🌹', bgColor: '#FFE5E5', category: '布丁' },
		{ name: '薄荷布丁', description: '清凉薄荷', price: 21, emoji: '🌿', bgColor: '#E8F5E8', category: '布丁' },
		{ name: '奶茶布丁', description: '港式奶茶', price: 24, emoji: '🧋', bgColor: '#F5E6D3', category: '布丁' },

		// 冰淇淋系列 (22个)
		{ name: '香草冰淇淋', description: '经典口味', price: 15, emoji: '🍦', bgColor: '#FFF8E1', category: '冰淇淋' },
		{ name: '巧克力冰淇淋', description: '浓郁可可', price: 18, emoji: '🍫', bgColor: '#F5E6D3', category: '冰淇淋' },
		{ name: '草莓冰淇淋', description: '新鲜果味', price: 18, emoji: '🍓', bgColor: '#FFE5E5', category: '冰淇淋' },
		{ name: '抹茶冰淇淋', description: '清香淡雅', price: 20, emoji: '🍵', bgColor: '#E8F5E8', category: '冰淇淋' },
		{ name: '芒果冰淇淋', description: '热带风情', price: 22, emoji: '🥭', bgColor: '#FFF3E0', category: '冰淇淋' },
		{ name: '蓝莓冰淇淋', description: '酸甜可口', price: 20, emoji: '🫐', bgColor: '#E6F3FF', category: '冰淇淋' },
		{ name: '薄荷冰淇淋', description: '清凉薄荷', price: 19, emoji: '🌿', bgColor: '#E8F5E8', category: '冰淇淋' },
		{ name: '咖啡冰淇淋', description: '醇香浓郁', price: 21, emoji: '☕', bgColor: '#F5E6D3', category: '冰淇淋' },
		{ name: '榴莲冰淇淋', description: '浓郁香甜', price: 28, emoji: '🌟', bgColor: '#FFF3E0', category: '冰淇淋' },
		{ name: '椰子冰淇淋', description: '热带椰香', price: 23, emoji: '🥥', bgColor: '#F8F8FF', category: '冰淇淋' },
		{ name: '柠檬冰淇淋', description: '清香酸甜', price: 18, emoji: '🍋', bgColor: '#FFFACD', category: '冰淇淋' },
		{ name: '焦糖冰淇淋', description: '香甜焦糖', price: 20, emoji: '🍮', bgColor: '#F5E6D3', category: '冰淇淋' },
		{ name: '樱花冰淇淋', description: '浪漫樱花', price: 25, emoji: '🌸', bgColor: '#FFE5E5', category: '冰淇淋' },
		{ name: '红豆冰淇淋', description: '日式红豆', price: 22, emoji: '🔴', bgColor: '#FFE5E5', category: '冰淇淋' },
		{ name: '绿茶冰淇淋', description: '清香绿茶', price: 20, emoji: '🍃', bgColor: '#E8F5E8', category: '冰淇淋' },
		{ name: '蜂蜜冰淇淋', description: '天然蜂蜜', price: 19, emoji: '🍯', bgColor: '#FFF8E1', category: '冰淇淋' },
		{ name: '玫瑰冰淇淋', description: '浪漫玫瑰', price: 24, emoji: '🌹', bgColor: '#FFE5E5', category: '冰淇淋' },
		{ name: '奶茶冰淇淋', description: '港式奶茶', price: 22, emoji: '🧋', bgColor: '#F5E6D3', category: '冰淇淋' },
		{ name: '芝士冰淇淋', description: '浓郁芝士', price: 24, emoji: '🧀', bgColor: '#FFF8E1', category: '冰淇淋' },
		{ name: '坚果冰淇淋', description: '香脆坚果', price: 23, emoji: '🥜', bgColor: '#F5E6D3', category: '冰淇淋' },
		{ name: '酸奶冰淇淋', description: '健康酸奶', price: 18, emoji: '🥛', bgColor: '#F8F8FF', category: '冰淇淋' },
		{ name: '黑芝麻冰淇淋', description: '香浓黑芝麻', price: 22, emoji: '⚫', bgColor: '#F5F5F5', category: '冰淇淋' },

		// 饼干系列 (25个)
		{ name: '巧克力饼干', description: '香脆可口', price: 12, emoji: '🍪', bgColor: '#F5E6D3', category: '饼干' },
		{ name: '燕麦饼干', description: '健康美味', price: 10, emoji: '🌾', bgColor: '#F5F5DC', category: '饼干' },
		{ name: '黄油饼干', description: '奶香浓郁', price: 15, emoji: '🧈', bgColor: '#FFF8E1', category: '饼干' },
		{ name: '杏仁饼干', description: '坚果香味', price: 18, emoji: '🥜', bgColor: '#F5E6D3', category: '饼干' },
		{ name: '抹茶饼干', description: '清香淡雅', price: 16, emoji: '🍵', bgColor: '#E8F5E8', category: '饼干' },
		{ name: '草莓饼干', description: '粉嫩可爱', price: 14, emoji: '🍓', bgColor: '#FFE5E5', category: '饼干' },
		{ name: '柠檬饼干', description: '清香怡人', price: 13, emoji: '🍋', bgColor: '#FFFACD', category: '饼干' },
		{ name: '椰子饼干', description: '热带椰香', price: 17, emoji: '🥥', bgColor: '#F8F8FF', category: '饼干' },
		{ name: '咖啡饼干', description: '醇香浓郁', price: 16, emoji: '☕', bgColor: '#F5E6D3', category: '饼干' },
		{ name: '蔓越莓饼干', description: '酸甜可口', price: 19, emoji: '🔴', bgColor: '#FFE5E5', category: '饼干' },
		{ name: '香草饼干', description: '经典香草', price: 12, emoji: '🤍', bgColor: '#FFF8E1', category: '饼干' },
		{ name: '芝士饼干', description: '浓郁芝士', price: 18, emoji: '🧀', bgColor: '#FFF8E1', category: '饼干' },
		{ name: '蜂蜜饼干', description: '天然蜂蜜', price: 14, emoji: '🍯', bgColor: '#FFF8E1', category: '饼干' },
		{ name: '玫瑰饼干', description: '浪漫玫瑰', price: 20, emoji: '🌹', bgColor: '#FFE5E5', category: '饼干' },
		{ name: '薄荷饼干', description: '清凉薄荷', price: 15, emoji: '🌿', bgColor: '#E8F5E8', category: '饼干' },
		{ name: '红豆饼干', description: '日式红豆', price: 16, emoji: '🔴', bgColor: '#FFE5E5', category: '饼干' },
		{ name: '绿茶饼干', description: '清香绿茶', price: 16, emoji: '🍃', bgColor: '#E8F5E8', category: '饼干' },
		{ name: '焦糖饼干', description: '香甜焦糖', price: 15, emoji: '🍮', bgColor: '#F5E6D3', category: '饼干' },
		{ name: '樱花饼干', description: '浪漫樱花', price: 22, emoji: '🌸', bgColor: '#FFE5E5', category: '饼干' },
		{ name: '奶茶饼干', description: '港式奶茶', price: 17, emoji: '🧋', bgColor: '#F5E6D3', category: '饼干' },
		{ name: '芒果饼干', description: '热带芒果', price: 16, emoji: '🥭', bgColor: '#FFF3E0', category: '饼干' },
		{ name: '蓝莓饼干', description: '酸甜蓝莓', price: 17, emoji: '🫐', bgColor: '#E6F3FF', category: '饼干' },
		{ name: '坚果饼干', description: '混合坚果', price: 20, emoji: '🥜', bgColor: '#F5E6D3', category: '饼干' },
		{ name: '黑芝麻饼干', description: '香浓黑芝麻', price: 16, emoji: '⚫', bgColor: '#F5F5F5', category: '饼干' },
		{ name: '紫薯饼干', description: '健康紫薯', price: 15, emoji: '🍠', bgColor: '#E6E6FA', category: '饼干' },

		// 甜甜圈系列 (15个)
		{ name: '巧克力甜甜圈', description: '经典巧克力', price: 15, emoji: '🍩', bgColor: '#F5E6D3', category: '甜甜圈' },
		{ name: '草莓甜甜圈', description: '粉嫩草莓', price: 16, emoji: '🍓', bgColor: '#FFE5E5', category: '甜甜圈' },
		{ name: '抹茶甜甜圈', description: '清香抹茶', price: 18, emoji: '🍵', bgColor: '#E8F5E8', category: '甜甜圈' },
		{ name: '香草甜甜圈', description: '经典香草', price: 14, emoji: '🤍', bgColor: '#FFF8E1', category: '甜甜圈' },
		{ name: '焦糖甜甜圈', description: '香甜焦糖', price: 17, emoji: '🍮', bgColor: '#F5E6D3', category: '甜甜圈' },
		{ name: '椰子甜甜圈', description: '热带椰香', price: 19, emoji: '🥥', bgColor: '#F8F8FF', category: '甜甜圈' },
		{ name: '柠檬甜甜圈', description: '清香柠檬', price: 16, emoji: '🍋', bgColor: '#FFFACD', category: '甜甜圈' },
		{ name: '蓝莓甜甜圈', description: '酸甜蓝莓', price: 18, emoji: '🫐', bgColor: '#E6F3FF', category: '甜甜圈' },
		{ name: '咖啡甜甜圈', description: '醇香咖啡', price: 17, emoji: '☕', bgColor: '#F5E6D3', category: '甜甜圈' },
		{ name: '芒果甜甜圈', description: '热带芒果', price: 18, emoji: '🥭', bgColor: '#FFF3E0', category: '甜甜圈' },
		{ name: '樱花甜甜圈', description: '浪漫樱花', price: 20, emoji: '🌸', bgColor: '#FFE5E5', category: '甜甜圈' },
		{ name: '红豆甜甜圈', description: '日式红豆', price: 17, emoji: '🔴', bgColor: '#FFE5E5', category: '甜甜圈' },
		{ name: '蜂蜜甜甜圈', description: '天然蜂蜜', price: 16, emoji: '🍯', bgColor: '#FFF8E1', category: '甜甜圈' },
		{ name: '玫瑰甜甜圈', description: '浪漫玫瑰', price: 19, emoji: '🌹', bgColor: '#FFE5E5', category: '甜甜圈' },
		{ name: '薄荷甜甜圈', description: '清凉薄荷', price: 17, emoji: '🌿', bgColor: '#E8F5E8', category: '甜甜圈' },

		// 司康饼系列 (12个)
		{ name: '原味司康饼', description: '英式经典', price: 12, emoji: '🥧', bgColor: '#FFF8E1', category: '司康饼' },
		{ name: '蓝莓司康饼', description: '酸甜蓝莓', price: 16, emoji: '🫐', bgColor: '#E6F3FF', category: '司康饼' },
		{ name: '巧克力司康饼', description: '浓郁巧克力', price: 15, emoji: '🍫', bgColor: '#F5E6D3', category: '司康饼' },
		{ name: '蔓越莓司康饼', description: '酸甜蔓越莓', price: 17, emoji: '🔴', bgColor: '#FFE5E5', category: '司康饼' },
		{ name: '柠檬司康饼', description: '清香柠檬', price: 14, emoji: '🍋', bgColor: '#FFFACD', category: '司康饼' },
		{ name: '杏仁司康饼', description: '坚果香味', price: 18, emoji: '🥜', bgColor: '#F5E6D3', category: '司康饼' },
		{ name: '草莓司康饼', description: '新鲜草莓', price: 16, emoji: '🍓', bgColor: '#FFE5E5', category: '司康饼' },
		{ name: '抹茶司康饼', description: '清香抹茶', price: 17, emoji: '🍵', bgColor: '#E8F5E8', category: '司康饼' },
		{ name: '香草司康饼', description: '经典香草', price: 13, emoji: '🤍', bgColor: '#FFF8E1', category: '司康饼' },
		{ name: '咖啡司康饼', description: '醇香咖啡', price: 16, emoji: '☕', bgColor: '#F5E6D3', category: '司康饼' },
		{ name: '椰子司康饼', description: '热带椰香', price: 17, emoji: '🥥', bgColor: '#F8F8FF', category: '司康饼' },
		{ name: '蜂蜜司康饼', description: '天然蜂蜜', price: 15, emoji: '🍯', bgColor: '#FFF8E1', category: '司康饼' },

		// 挞类系列 (15个)
		{ name: '蛋挞', description: '港式经典', price: 8, emoji: '🥧', bgColor: '#FFF8E1', category: '挞类' },
		{ name: '水果挞', description: '新鲜水果', price: 25, emoji: '🍓', bgColor: '#FFE5E5', category: '挞类' },
		{ name: '巧克力挞', description: '浓郁巧克力', price: 22, emoji: '🍫', bgColor: '#F5E6D3', category: '挞类' },
		{ name: '柠檬挞', description: '清香酸甜', price: 20, emoji: '🍋', bgColor: '#FFFACD', category: '挞类' },
		{ name: '芝士挞', description: '浓郁芝士', price: 24, emoji: '🧀', bgColor: '#FFF8E1', category: '挞类' },
		{ name: '抹茶挞', description: '清香抹茶', price: 23, emoji: '🍵', bgColor: '#E8F5E8', category: '挞类' },
		{ name: '草莓挞', description: '新鲜草莓', price: 22, emoji: '🍓', bgColor: '#FFE5E5', category: '挞类' },
		{ name: '芒果挞', description: '热带芒果', price: 24, emoji: '🥭', bgColor: '#FFF3E0', category: '挞类' },
		{ name: '蓝莓挞', description: '酸甜蓝莓', price: 23, emoji: '🫐', bgColor: '#E6F3FF', category: '挞类' },
		{ name: '椰子挞', description: '热带椰香', price: 22, emoji: '🥥', bgColor: '#F8F8FF', category: '挞类' },
		{ name: '咖啡挞', description: '醇香咖啡', price: 21, emoji: '☕', bgColor: '#F5E6D3', category: '挞类' },
		{ name: '焦糖挞', description: '香甜焦糖', price: 20, emoji: '🍮', bgColor: '#F5E6D3', category: '挞类' },
		{ name: '樱花挞', description: '浪漫樱花', price: 26, emoji: '🌸', bgColor: '#FFE5E5', category: '挞类' },
		{ name: '红豆挞', description: '日式红豆', price: 21, emoji: '🔴', bgColor: '#FFE5E5', category: '挞类' },
		{ name: '蜂蜜挞', description: '天然蜂蜜', price: 19, emoji: '🍯', bgColor: '#FFF8E1', category: '挞类' },

		// 慕斯系列 (18个)
		{ name: '巧克力慕斯', description: '丝滑巧克力', price: 35, emoji: '🍫', bgColor: '#F5E6D3', category: '慕斯' },
		{ name: '草莓慕斯', description: '粉嫩草莓', price: 32, emoji: '🍓', bgColor: '#FFE5E5', category: '慕斯' },
		{ name: '芒果慕斯', description: '热带芒果', price: 38, emoji: '🥭', bgColor: '#FFF3E0', category: '慕斯' },
		{ name: '抹茶慕斯', description: '清香抹茶', price: 36, emoji: '🍵', bgColor: '#E8F5E8', category: '慕斯' },
		{ name: '香草慕斯', description: '经典香草', price: 30, emoji: '🤍', bgColor: '#FFF8E1', category: '慕斯' },
		{ name: '柠檬慕斯', description: '清香柠檬', price: 33, emoji: '🍋', bgColor: '#FFFACD', category: '慕斯' },
		{ name: '蓝莓慕斯', description: '酸甜蓝莓', price: 35, emoji: '🫐', bgColor: '#E6F3FF', category: '慕斯' },
		{ name: '椰子慕斯', description: '热带椰香', price: 34, emoji: '🥥', bgColor: '#F8F8FF', category: '慕斯' },
		{ name: '咖啡慕斯', description: '醇香咖啡', price: 36, emoji: '☕', bgColor: '#F5E6D3', category: '慕斯' },
		{ name: '焦糖慕斯', description: '香甜焦糖', price: 33, emoji: '🍮', bgColor: '#F5E6D3', category: '慕斯' },
		{ name: '樱花慕斯', description: '浪漫樱花', price: 40, emoji: '🌸', bgColor: '#FFE5E5', category: '慕斯' },
		{ name: '红豆慕斯', description: '日式红豆', price: 34, emoji: '🔴', bgColor: '#FFE5E5', category: '慕斯' },
		{ name: '绿茶慕斯', description: '清香绿茶', price: 36, emoji: '🍃', bgColor: '#E8F5E8', category: '慕斯' },
		{ name: '蜂蜜慕斯', description: '天然蜂蜜', price: 32, emoji: '🍯', bgColor: '#FFF8E1', category: '慕斯' },
		{ name: '玫瑰慕斯', description: '浪漫玫瑰', price: 38, emoji: '🌹', bgColor: '#FFE5E5', category: '慕斯' },
		{ name: '薄荷慕斯', description: '清凉薄荷', price: 33, emoji: '🌿', bgColor: '#E8F5E8', category: '慕斯' },
		{ name: '奶茶慕斯', description: '港式奶茶', price: 36, emoji: '🧋', bgColor: '#F5E6D3', category: '慕斯' },
		{ name: '芝士慕斯', description: '浓郁芝士', price: 38, emoji: '🧀', bgColor: '#FFF8E1', category: '慕斯' },

		// 特色甜品 (30个)
		{ name: '舒芙蕾松饼', description: '日式松饼', price: 42, emoji: '🥞', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '法式可丽饼', description: '法式薄饼', price: 28, emoji: '🥞', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '意式奶昔', description: '浓郁奶昔', price: 25, emoji: '🥤', bgColor: '#F8F8FF', category: '特色甜品' },
		{ name: '英式下午茶', description: '精致茶点', price: 68, emoji: '🫖', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '法式马德琳', description: '贝壳小蛋糕', price: 15, emoji: '🐚', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '意式卡诺里', description: '西西里甜品', price: 32, emoji: '🥖', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '日式大福', description: '软糯大福', price: 18, emoji: '🍡', bgColor: '#F8F8FF', category: '特色甜品' },
		{ name: '韩式雪花冰', description: '绵密雪花冰', price: 35, emoji: '🍧', bgColor: '#F0F8FF', category: '特色甜品' },
		{ name: '泰式椰汁糕', description: '椰香浓郁', price: 22, emoji: '🥥', bgColor: '#F8F8FF', category: '特色甜品' },
		{ name: '德式黑森林', description: '经典黑森林', price: 58, emoji: '🌲', bgColor: '#F5E6D3', category: '特色甜品' },
		{ name: '奥地利萨赫蛋糕', description: '巧克力杏仁', price: 65, emoji: '🍫', bgColor: '#F5E6D3', category: '特色甜品' },
		{ name: '西班牙吉拿棒', description: '肉桂糖粉', price: 20, emoji: '🥖', bgColor: '#F5E6D3', category: '特色甜品' },
		{ name: '土耳其软糖', description: '玫瑰味软糖', price: 25, emoji: '🌹', bgColor: '#FFE5E5', category: '特色甜品' },
		{ name: '印度奶球', description: '香甜奶球', price: 18, emoji: '⚪', bgColor: '#F8F8FF', category: '特色甜品' },
		{ name: '墨西哥三奶蛋糕', description: '三种奶制作', price: 45, emoji: '🥛', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '希腊蜂蜜酥', description: '层层酥脆', price: 38, emoji: '🍯', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '俄式鸟奶蛋糕', description: '轻盈如云', price: 48, emoji: '☁️', bgColor: '#F8F8FF', category: '特色甜品' },
		{ name: '波兰玫瑰饼', description: '玫瑰花香', price: 28, emoji: '🌹', bgColor: '#FFE5E5', category: '特色甜品' },
		{ name: '匈牙利多博斯蛋糕', description: '焦糖顶层', price: 52, emoji: '🍮', bgColor: '#F5E6D3', category: '特色甜品' },
		{ name: '瑞士卷', description: '奶油卷心', price: 35, emoji: '🌀', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '比利时华夫饼', description: '格子饼干', price: 25, emoji: '🧇', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '荷兰迷你煎饼', description: '小巧可爱', price: 22, emoji: '🥞', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '丹麦酥', description: '层次分明', price: 18, emoji: '🥐', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '挪威公主蛋糕', description: '绿色马卡龙粉', price: 68, emoji: '👸', bgColor: '#E8F5E8', category: '特色甜品' },
		{ name: '芬兰肉桂卷', description: '温暖肉桂', price: 20, emoji: '🌀', bgColor: '#F5E6D3', category: '特色甜品' },
		{ name: '冰岛酸奶蛋糕', description: '清爽酸奶', price: 38, emoji: '🥛', bgColor: '#F8F8FF', category: '特色甜品' },
		{ name: '爱尔兰咖啡慕斯', description: '威士忌香', price: 45, emoji: '☕', bgColor: '#F5E6D3', category: '特色甜品' },
		{ name: '苏格兰短面包', description: '黄油香酥', price: 16, emoji: '🍪', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '威尔士蛋糕', description: '传统配方', price: 22, emoji: '🍰', bgColor: '#FFF8E1', category: '特色甜品' },
		{ name: '新西兰帕芙洛娃', description: '蛋白霜底', price: 42, emoji: '☁️', bgColor: '#F8F8FF', category: '特色甜品' }
	])

	// 搜索结果
	const searchResults = computed(() => {
		if (!searchKeyword.value || !hasSearched.value) return []

		const keyword = searchKeyword.value.toLowerCase()
		return allProducts.value.filter(product =>
			product.name.toLowerCase().includes(keyword) ||
			product.description.toLowerCase().includes(keyword) ||
			product.category.toLowerCase().includes(keyword)
		)
	})

	// 总结果数量
	const totalResults = computed(() => searchResults.value.length)

	// 模拟异步搜索数据加载
	const loadSearchData = async (page = 1, isRefresh = false) => {
		return new Promise((resolve) => {
			setTimeout(() => {
				const results = searchResults.value
				const startIndex = (page - 1) * pageSize.value
				const endIndex = startIndex + pageSize.value
				const pageData = results.slice(startIndex, endIndex)

				resolve({
					data: pageData,
					hasMore: endIndex < results.length,
					total: results.length
				})
			}, isRefresh ? 800 : 500) // 刷新时稍微长一点的延迟
		})
	}

	// 执行搜索
	const performSearch = async (isRefresh = false) => {
		if (!isRefresh) {
			isLoading.value = true
		}

		try {
			currentPage.value = 1
			const result = await loadSearchData(1, isRefresh)

			displayResults.value = result.data
			hasMore.value = result.hasMore

			if (!isRefresh) {
				isLoading.value = false
			}
		} catch (error) {
			console.error('搜索失败:', error)
			if (!isRefresh) {
				isLoading.value = false
			}
			uni.showToast({
				title: '搜索失败，请重试',
				icon: 'none'
			})
		}
	}

	// 下拉刷新
	const onRefresh = async () => {
		isRefreshing.value = true
		await performSearch(true)
	}

	// 刷新完成
	const onRefreshRestore = () => {
		isRefreshing.value = false
	}

	// 上拉加载更多
	const onLoadMore = async () => {
		if (isLoadingMore.value || !hasMore.value) return

		isLoadingMore.value = true

		try {
			const nextPage = currentPage.value + 1
			const result = await loadSearchData(nextPage)

			displayResults.value = [...displayResults.value, ...result.data]
			hasMore.value = result.hasMore
			currentPage.value = nextPage
		} catch (error) {
			console.error('加载更多失败:', error)
			uni.showToast({
				title: '加载失败，请重试',
				icon: 'none'
			})
		} finally {
			isLoadingMore.value = false
		}
	}

	// 搜索输入处理
	const onSearchInput = () => {
		if (searchKeyword.value.length > 0) {
			generateSuggestions()
			showSuggestions.value = true
		} else {
			showSuggestions.value = false
			hasSearched.value = false
			displayResults.value = []
		}
	}

	// 生成搜索建议
	const generateSuggestions = () => {
		const keyword = searchKeyword.value.toLowerCase()
		const allSuggestions = [
			...allProducts.value.map(p => p.name),
			...allProducts.value.map(p => p.category),
			'巧克力系列', '草莓系列', '抹茶系列', '经典口味', '新品推荐'
		]

		suggestions.value = [...new Set(allSuggestions)]
			.filter(item => item.toLowerCase().includes(keyword))
			.slice(0, 5)
	}

	// 搜索确认
	const onSearchConfirm = async () => {
		if (searchKeyword.value.trim()) {
			hasSearched.value = true
			showSuggestions.value = false
			await performSearch()
		}
	}

	// 选择搜索建议
	const selectSuggestion = async (suggestion) => {
		searchKeyword.value = suggestion
		await onSearchConfirm()
	}

	// 选择热门标签
	const selectHotTag = async (tag) => {
		searchKeyword.value = tag
		await onSearchConfirm()
	}

	// 返回上一页
	const goBack = () => {
		uni.navigateBack()
	}

	// 跳转到商品详情
	const goToProduct = (product) => {
		uni.navigateTo({
			url: `/pages/product/product?id=${product.name}&name=${product.name}&price=${product.price}&emoji=${product.emoji}&bgColor=${encodeURIComponent(product.bgColor)}`
		})
	}

	// 添加到购物车
	const addToCart = (product) => {
		uni.showToast({
			title: '已添加到购物车',
			icon: 'success'
		})
	}

	// 页面加载时处理URL参数
	onMounted(() => {
		const pages = getCurrentPages()
		const currentPageInfo = pages[pages.length - 1]
		const options = currentPageInfo.options || {}

		if (options.keyword) {
			searchKeyword.value = decodeURIComponent(options.keyword)
			onSearchConfirm()
		}
	})
</script>

<style scoped>
	.container {
		background-color: #f8f9fa;
		min-height: 100vh;
	}

	/* 搜索头部 */
	.search-header {
		background-color: #fff;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #eee;
	}

	.search-bar {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background-color: #f5f5f5;
	}

	.back-icon {
		font-size: 32rpx;
		color: #333;
	}

	.search-input-wrapper {
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		border-radius: 50rpx;
		padding: 0 30rpx;
	}

	.search-input {
		flex: 1;
		height: 70rpx;
		font-size: 28rpx;
		color: #333;
		background: transparent;
		border: none;
		outline: none;
	}

	.search-btn {
		width: 50rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.search-icon {
		font-size: 28rpx;
		color: #999;
	}

	/* 搜索建议 */
	.search-suggestions {
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
	}

	.suggestion-title {
		padding: 20rpx 30rpx 10rpx;
		font-size: 24rpx;
		color: #999;
	}

	.suggestion-item {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		gap: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.suggestion-icon {
		font-size: 24rpx;
		color: #999;
	}

	.suggestion-text {
		font-size: 28rpx;
		color: #333;
	}

	/* 热门搜索 */
	.hot-search {
		background-color: #fff;
		margin-top: 20rpx;
		padding: 30rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.hot-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.hot-tag {
		padding: 15rpx 30rpx;
		background-color: #f5f5f5;
		border-radius: 50rpx;
		font-size: 24rpx;
		color: #666;
	}

	.hot-tag:active {
		background-color: #ff6b6b;
		color: #fff;
	}

	/* 搜索结果滚动容器 */
	.search-results-scroll {
		height: calc(100vh - 140rpx); /* 减去搜索栏高度 */
		background-color: #f8f9fa;
	}

	/* 搜索结果 */
	.search-results {
		padding: 30rpx;
	}

	.result-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.result-count {
		font-size: 28rpx;
		color: #333;
	}

	.filter-btn {
		display: flex;
		align-items: center;
		gap: 10rpx;
		padding: 15rpx 25rpx;
		background-color: #f5f5f5;
		border-radius: 50rpx;
	}

	.filter-text {
		font-size: 24rpx;
		color: #666;
	}

	.filter-icon {
		font-size: 20rpx;
	}

	/* 结果列表 */
	.results-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.result-item {
		display: flex;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		gap: 20rpx;
	}

	.product-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
	}

	.product-emoji {
		font-size: 60rpx;
	}

	.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.product-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.product-desc {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 20rpx;
	}

	.product-price-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.product-price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff6b6b;
	}

	.add-btn {
		width: 60rpx;
		height: 60rpx;
		background-color: #ff6b6b;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.add-icon {
		color: #fff;
		font-size: 28rpx;
		font-weight: bold;
	}

	/* 无结果 */
	.no-results {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 100rpx 0;
		gap: 20rpx;
	}

	.no-results-emoji {
		font-size: 100rpx;
	}

	.no-results-text {
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}

	.no-results-tip {
		font-size: 24rpx;
		color: #999;
	}

	/* 加载更多 */
	.load-more {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx 0;
	}

	.load-more-content {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.load-more-text {
		font-size: 24rpx;
		color: #999;
	}

	/* 没有更多 */
	.no-more {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx 0;
	}

	.no-more-text {
		font-size: 24rpx;
		color: #ccc;
	}

	/* 初始加载状态 */
	.loading-initial {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
		gap: 20rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #999;
	}

	/* 加载动画 */
	.loading-spinner {
		width: 40rpx;
		height: 40rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #ff6b6b;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
</style>
